<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统功能模块图</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            margin: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 24px;
        }
        .diagram {
            width: 100%;
            height: 600px;
            border: 1px solid #ccc;
            background-color: #ffffff;
        }
        .module-box {
            fill: #ffffff;
            stroke: #000000;
            stroke-width: 2;
        }
        .module-text {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            text-anchor: middle;
            dominant-baseline: middle;
            fill: #000000;
        }
        .module-title {
            font-weight: bold;
            font-size: 16px;
        }
        .connection-line {
            stroke: #000000;
            stroke-width: 2;
            fill: none;
        }
        .arrow {
            fill: #000000;
            stroke: #000000;
            stroke-width: 1;
        }
        .interface-box {
            fill: #f0f0f0;
            stroke: #000000;
            stroke-width: 1;
        }
        .interface-text {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            text-anchor: middle;
            dominant-baseline: middle;
            fill: #333333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>系统功能模块图</h1>
        <svg class="diagram" viewBox="0 0 900 550">
            <!-- 主程序模块 -->
            <rect class="module-box" x="300" y="50" width="300" height="80" rx="5"/>
            <text class="module-text module-title" x="450" y="75">主程序模块</text>
            <text class="module-text" x="450" y="95">(main.py)</text>
            <text class="module-text" x="450" y="115">GUI界面控制</text>
            
            <!-- 连接线从主程序到各子模块 -->
            <line class="connection-line" x1="450" y1="130" x2="450" y2="180"/>
            
            <!-- 分支线 -->
            <line class="connection-line" x1="200" y1="200" x2="700" y2="200"/>
            <line class="connection-line" x1="200" y1="200" x2="200" y2="220"/>
            <line class="connection-line" x1="450" y1="200" x2="450" y2="220"/>
            <line class="connection-line" x1="700" y1="200" x2="700" y2="220"/>
            
            <!-- 箭头 -->
            <polygon class="arrow" points="195,215 200,220 205,215"/>
            <polygon class="arrow" points="445,215 450,220 455,215"/>
            <polygon class="arrow" points="695,215 700,220 705,215"/>
            
            <!-- 属性文法模块 -->
            <rect class="module-box" x="50" y="240" width="300" height="120" rx="5"/>
            <text class="module-text module-title" x="200" y="270">属性文法显示模块</text>
            <text class="module-text" x="200" y="290">(grammar.py)</text>
            <text class="module-text" x="200" y="315">• 文法规则定义</text>
            <text class="module-text" x="200" y="335">• 属性文法显示</text>
            <text class="module-text" x="200" y="355">• 文法说明输出</text>
            
            <!-- 语法分析翻译模块 -->
            <rect class="module-box" x="375" y="240" width="300" height="120" rx="5"/>
            <text class="module-text module-title" x="525" y="270">语法分析翻译模块</text>
            <text class="module-text" x="525" y="290">(parser.py)</text>
            <text class="module-text" x="525" y="315">• 词法分析</text>
            <text class="module-text" x="525" y="335">• 语法分析</text>
            <text class="module-text" x="525" y="355">• 语法制导翻译</text>
            
            <!-- 逆波兰式求值模块 -->
            <rect class="module-box" x="700" y="240" width="300" height="120" rx="5"/>
            <text class="module-text module-title" x="850" y="270">逆波兰式求值模块</text>
            <text class="module-text" x="850" y="290">(evaluator.py)</text>
            <text class="module-text" x="850" y="315">• 后缀表达式解析</text>
            <text class="module-text" x="850" y="335">• 栈式求值算法</text>
            <text class="module-text" x="850" y="355">• 变量值管理</text>
            
            <!-- 数据流接口 -->
            <rect class="interface-box" x="50" y="400" width="150" height="40" rx="3"/>
            <text class="interface-text" x="125" y="420">文法规则数据</text>
            
            <rect class="interface-box" x="250" y="400" width="150" height="40" rx="3"/>
            <text class="interface-text" x="325" y="420">中缀表达式</text>
            
            <rect class="interface-box" x="450" y="400" width="150" height="40" rx="3"/>
            <text class="interface-text" x="525" y="420">逆波兰式</text>
            
            <rect class="interface-box" x="650" y="400" width="150" height="40" rx="3"/>
            <text class="interface-text" x="725" y="420">计算结果</text>
            
            <!-- 数据流箭头 -->
            <line class="connection-line" x1="125" y1="360" x2="125" y2="400"/>
            <polygon class="arrow" points="120,395 125,400 130,395"/>
            
            <line class="connection-line" x1="325" y1="360" x2="325" y2="400"/>
            <line class="connection-line" x1="400" y1="420" x2="450" y2="420"/>
            <polygon class="arrow" points="445,415 450,420 445,425"/>
            
            <line class="connection-line" x1="600" y1="420" x2="650" y2="420"/>
            <polygon class="arrow" points="645,415 650,420 645,425"/>
            
            <line class="connection-line" x1="725" y1="360" x2="725" y2="400"/>
            <polygon class="arrow" points="720,395 725,400 730,395"/>
            
            <!-- 模块间协作关系 -->
            <line class="connection-line" x1="350" y1="300" x2="375" y2="300" stroke-dasharray="5,5"/>
            <line class="connection-line" x1="675" y1="300" x2="700" y2="300" stroke-dasharray="5,5"/>
            
            <!-- 图例 -->
            <g transform="translate(50, 480)">
                <text class="module-text" x="0" y="0" style="font-weight: bold;">图例:</text>
                <line class="connection-line" x1="0" y1="20" x2="30" y2="20"/>
                <polygon class="arrow" points="25,15 30,20 25,25"/>
                <text class="module-text" x="40" y="25" style="font-size: 12px;">控制流</text>
                
                <line class="connection-line" x1="120" y1="20" x2="150" y2="20" stroke-dasharray="5,5"/>
                <text class="module-text" x="160" y="25" style="font-size: 12px;">数据依赖</text>
            </g>
        </svg>
    </div>
</body>
</html>
