<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>逆波兰式求值流程图</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            margin: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 24px;
        }
        .diagram {
            width: 100%;
            height: 1000px;
            border: 1px solid #ccc;
            background-color: #ffffff;
        }
        .start-end {
            fill: #ffffff;
            stroke: #000000;
            stroke-width: 2;
        }
        .process {
            fill: #ffffff;
            stroke: #000000;
            stroke-width: 2;
        }
        .decision {
            fill: #ffffff;
            stroke: #000000;
            stroke-width: 2;
        }
        .text {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            text-anchor: middle;
            dominant-baseline: middle;
            fill: #000000;
        }
        .small-text {
            font-size: 12px;
        }
        .flow-line {
            stroke: #000000;
            stroke-width: 2;
            fill: none;
        }
        .arrow {
            fill: #000000;
            stroke: #000000;
            stroke-width: 1;
        }
        .label {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            text-anchor: middle;
            dominant-baseline: middle;
            fill: #000000;
        }
        .stack-visual {
            fill: #f0f0f0;
            stroke: #000000;
            stroke-width: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>逆波兰式求值流程图</h1>
        <svg class="diagram" viewBox="0 0 700 950">
            <!-- 开始 -->
            <ellipse class="start-end" cx="350" cy="50" rx="60" ry="25"/>
            <text class="text" x="350" y="50">开始</text>
            
            <!-- 输入逆波兰式 -->
            <rect class="process" x="250" y="100" width="200" height="50" rx="5"/>
            <text class="text" x="350" y="120">输入逆波兰式</text>
            <text class="text small-text" x="350" y="135">和变量值</text>
            
            <!-- 初始化栈 -->
            <rect class="process" x="250" y="180" width="200" height="50" rx="5"/>
            <text class="text" x="350" y="200">初始化空栈</text>
            <text class="text small-text" x="350" y="215">设置扫描指针</text>
            
            <!-- 栈的可视化 -->
            <g transform="translate(500, 180)">
                <rect class="stack-visual" x="0" y="0" width="80" height="200" rx="3"/>
                <text class="small-text" x="40" y="-10" style="text-anchor: middle;">操作数栈</text>
                <line stroke="#ccc" x1="10" y1="160" x2="70" y2="160"/>
                <line stroke="#ccc" x1="10" y1="120" x2="70" y2="120"/>
                <line stroke="#ccc" x1="10" y1="80" x2="70" y2="80"/>
                <line stroke="#ccc" x1="10" y1="40" x2="70" y2="40"/>
                <text class="small-text" x="90" y="180">栈底</text>
                <text class="small-text" x="90" y="50">栈顶</text>
            </g>
            
            <!-- 从左到右扫描 -->
            <rect class="process" x="250" y="260" width="200" height="50" rx="5"/>
            <text class="text" x="350" y="280">从左到右扫描</text>
            <text class="text small-text" x="350" y="295">读取下一个Token</text>
            
            <!-- 判断Token类型 -->
            <polygon class="decision" points="350,340 420,380 350,420 280,380"/>
            <text class="text small-text" x="350" y="375">Token</text>
            <text class="text small-text" x="350" y="390">类型？</text>
            
            <!-- 操作数分支 -->
            <rect class="process" x="480" y="355" width="150" height="50" rx="5"/>
            <text class="text small-text" x="555" y="375">操作数</text>
            <text class="text small-text" x="555" y="390">入栈</text>
            
            <!-- 运算符分支 -->
            <rect class="process" x="70" y="355" width="150" height="50" rx="5"/>
            <text class="text small-text" x="145" y="375">运算符</text>
            <text class="text small-text" x="145" y="390">处理运算</text>
            
            <!-- 判断运算符类型 -->
            <polygon class="decision" points="145,440 195,470 145,500 95,470"/>
            <text class="text small-text" x="145" y="465">一元还是</text>
            <text class="text small-text" x="145" y="480">二元？</text>
            
            <!-- 一元运算符处理 -->
            <rect class="process" x="20" y="530" width="120" height="60" rx="5"/>
            <text class="text small-text" x="80" y="550">弹出1个操作数</text>
            <text class="text small-text" x="80" y="565">执行一元运算</text>
            <text class="text small-text" x="80" y="580">结果入栈</text>
            
            <!-- 二元运算符处理 -->
            <rect class="process" x="170" y="530" width="120" height="60" rx="5"/>
            <text class="text small-text" x="230" y="550">弹出2个操作数</text>
            <text class="text small-text" x="230" y="565">执行二元运算</text>
            <text class="text small-text" x="230" y="580">结果入栈</text>
            
            <!-- 合并点 -->
            <circle cx="350" cy="630" r="5" fill="#000"/>
            
            <!-- 判断是否扫描完毕 -->
            <polygon class="decision" points="350,670 420,700 350,730 280,700"/>
            <text class="text small-text" x="350" y="695">扫描</text>
            <text class="text small-text" x="350" y="710">完毕？</text>
            
            <!-- 检查栈状态 -->
            <polygon class="decision" points="150,770 220,800 150,830 80,800"/>
            <text class="text small-text" x="150" y="795">栈中只有</text>
            <text class="text small-text" x="150" y="810">一个元素？</text>
            
            <!-- 输出结果 -->
            <rect class="process" x="450" y="775" width="200" height="50" rx="5"/>
            <text class="text" x="550" y="795">输出栈顶元素</text>
            <text class="text small-text" x="550" y="810">作为最终结果</text>
            
            <!-- 错误处理 -->
            <rect class="process" x="50" y="860" width="200" height="50" rx="5"/>
            <text class="text" x="150" y="880">输出错误信息</text>
            <text class="text small-text" x="150" y="895">表达式格式错误</text>
            
            <!-- 结束 -->
            <ellipse class="start-end" cx="350" cy="920" rx="60" ry="25"/>
            <text class="text" x="350" y="920">结束</text>
            
            <!-- 连接线和箭头 -->
            <!-- 开始到输入 -->
            <line class="flow-line" x1="350" y1="75" x2="350" y2="100"/>
            <polygon class="arrow" points="345,95 350,100 355,95"/>
            
            <!-- 输入到初始化 -->
            <line class="flow-line" x1="350" y1="150" x2="350" y2="180"/>
            <polygon class="arrow" points="345,175 350,180 355,175"/>
            
            <!-- 初始化到扫描 -->
            <line class="flow-line" x1="350" y1="230" x2="350" y2="260"/>
            <polygon class="arrow" points="345,255 350,260 355,255"/>
            
            <!-- 扫描到判断 -->
            <line class="flow-line" x1="350" y1="310" x2="350" y2="340"/>
            <polygon class="arrow" points="345,335 350,340 355,335"/>
            
            <!-- 判断到操作数 -->
            <line class="flow-line" x1="420" y1="380" x2="480" y2="380"/>
            <polygon class="arrow" points="475,375 480,380 475,385"/>
            <text class="label" x="450" y="370">操作数</text>
            
            <!-- 判断到运算符 -->
            <line class="flow-line" x1="280" y1="380" x2="220" y2="380"/>
            <polygon class="arrow" points="225,375 220,380 225,385"/>
            <text class="label" x="250" y="370">运算符</text>
            
            <!-- 操作数入栈后到合并点 -->
            <line class="flow-line" x1="555" y1="405" x2="555" y2="630"/>
            <line class="flow-line" x1="555" y1="630" x2="355" y2="630"/>
            <polygon class="arrow" points="360,625 355,630 360,635"/>
            
            <!-- 运算符到判断运算符类型 -->
            <line class="flow-line" x1="145" y1="405" x2="145" y2="440"/>
            <polygon class="arrow" points="140,435 145,440 150,435"/>
            
            <!-- 一元运算符 -->
            <line class="flow-line" x1="95" y1="470" x2="80" y2="470"/>
            <line class="flow-line" x1="80" y1="470" x2="80" y2="530"/>
            <polygon class="arrow" points="75,525 80,530 85,525"/>
            <text class="label" x="87" y="460">一元(@)</text>
            
            <!-- 二元运算符 -->
            <line class="flow-line" x1="195" y1="470" x2="230" y2="470"/>
            <line class="flow-line" x1="230" y1="470" x2="230" y2="530"/>
            <polygon class="arrow" points="225,525 230,530 235,525"/>
            <text class="label" x="212" y="460">二元</text>
            
            <!-- 运算结果到合并点 -->
            <line class="flow-line" x1="80" y1="590" x2="80" y2="630"/>
            <line class="flow-line" x1="80" y1="630" x2="345" y2="630"/>
            <polygon class="arrow" points="340,625 345,630 340,635"/>
            
            <line class="flow-line" x1="230" y1="590" x2="230" y2="630"/>
            <line class="flow-line" x1="230" y1="630" x2="345" y2="630"/>
            <polygon class="arrow" points="340,625 345,630 340,635"/>
            
            <!-- 合并点到判断扫描完毕 -->
            <line class="flow-line" x1="350" y1="635" x2="350" y2="670"/>
            <polygon class="arrow" points="345,665 350,670 355,665"/>
            
            <!-- 未扫描完毕循环 -->
            <line class="flow-line" x1="420" y1="700" x2="500" y2="700"/>
            <line class="flow-line" x1="500" y1="700" x2="500" y2="280"/>
            <line class="flow-line" x1="500" y1="280" x2="450" y2="280"/>
            <polygon class="arrow" points="455,275 450,280 455,285"/>
            <text class="label" x="460" y="690">否</text>
            
            <!-- 扫描完毕到检查栈 -->
            <line class="flow-line" x1="280" y1="700" x2="220" y2="700"/>
            <line class="flow-line" x1="220" y1="700" x2="220" y2="770"/>
            <polygon class="arrow" points="215,765 220,770 225,765"/>
            <text class="label" x="250" y="690">是</text>
            
            <!-- 栈正确到输出 -->
            <line class="flow-line" x1="220" y1="800" x2="450" y2="800"/>
            <polygon class="arrow" points="445,795 450,800 445,805"/>
            <text class="label" x="335" y="790">是</text>
            
            <!-- 栈错误 -->
            <line class="flow-line" x1="80" y1="800" x2="50" y2="800"/>
            <line class="flow-line" x1="50" y1="800" x2="50" y2="885"/>
            <line class="flow-line" x1="50" y1="885" x2="50" y2="885"/>
            <polygon class="arrow" points="45,880 50,885 55,880"/>
            <text class="label" x="65" y="790">否</text>
            
            <!-- 输出到结束 -->
            <line class="flow-line" x1="550" y1="825" x2="550" y2="920"/>
            <line class="flow-line" x1="550" y1="920" x2="410" y2="920"/>
            <polygon class="arrow" points="415,915 410,920 415,925"/>
            
            <!-- 错误到结束 -->
            <line class="flow-line" x1="150" y1="910" x2="150" y2="920"/>
            <line class="flow-line" x1="150" y1="920" x2="290" y2="920"/>
            <polygon class="arrow" points="285,915 290,920 285,925"/>
        </svg>
    </div>
</body>
</html>
