<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语法制导翻译流程图</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            margin: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 24px;
        }
        .diagram {
            width: 100%;
            height: 900px;
            border: 1px solid #ccc;
            background-color: #ffffff;
        }
        .start-end {
            fill: #ffffff;
            stroke: #000000;
            stroke-width: 2;
        }
        .process {
            fill: #ffffff;
            stroke: #000000;
            stroke-width: 2;
        }
        .decision {
            fill: #ffffff;
            stroke: #000000;
            stroke-width: 2;
        }
        .text {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            text-anchor: middle;
            dominant-baseline: middle;
            fill: #000000;
        }
        .small-text {
            font-size: 12px;
        }
        .flow-line {
            stroke: #000000;
            stroke-width: 2;
            fill: none;
        }
        .arrow {
            fill: #000000;
            stroke: #000000;
            stroke-width: 1;
        }
        .label {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            text-anchor: middle;
            dominant-baseline: middle;
            fill: #000000;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>语法制导翻译流程图</h1>
        <svg class="diagram" viewBox="0 0 700 850">
            <!-- 开始 -->
            <ellipse class="start-end" cx="350" cy="50" rx="60" ry="25"/>
            <text class="text" x="350" y="50">开始</text>
            
            <!-- 输入中缀表达式 -->
            <rect class="process" x="250" y="100" width="200" height="50" rx="5"/>
            <text class="text" x="350" y="125">输入中缀表达式</text>
            
            <!-- 初始化 -->
            <rect class="process" x="250" y="180" width="200" height="50" rx="5"/>
            <text class="text" x="350" y="200">初始化词法分析器</text>
            <text class="text small-text" x="350" y="215">和语法分析器</text>
            
            <!-- 词法分析 -->
            <rect class="process" x="250" y="260" width="200" height="50" rx="5"/>
            <text class="text" x="350" y="280">词法分析</text>
            <text class="text small-text" x="350" y="295">生成Token序列</text>
            
            <!-- 语法分析开始 -->
            <rect class="process" x="250" y="340" width="200" height="50" rx="5"/>
            <text class="text" x="350" y="360">调用parse_expression()</text>
            <text class="text small-text" x="350" y="375">递归下降分析</text>
            
            <!-- 判断Token类型 -->
            <polygon class="decision" points="350,420 420,450 350,480 280,450"/>
            <text class="text small-text" x="350" y="445">当前Token</text>
            <text class="text small-text" x="350" y="460">类型？</text>
            
            <!-- 操作数处理 -->
            <rect class="process" x="480" y="425" width="150" height="50" rx="5"/>
            <text class="text small-text" x="555" y="445">操作数</text>
            <text class="text small-text" x="555" y="460">直接输出到后缀式</text>
            
            <!-- 运算符处理 -->
            <rect class="process" x="100" y="425" width="150" height="50" rx="5"/>
            <text class="text small-text" x="175" y="445">运算符</text>
            <text class="text small-text" x="175" y="460">按优先级处理</text>
            
            <!-- 语义动作 -->
            <rect class="process" x="250" y="520" width="200" height="50" rx="5"/>
            <text class="text" x="350" y="540">执行语义动作</text>
            <text class="text small-text" x="350" y="555">生成逆波兰式片段</text>
            
            <!-- 判断是否还有Token -->
            <polygon class="decision" points="350,600 420,630 350,660 280,630"/>
            <text class="text small-text" x="350" y="625">还有</text>
            <text class="text small-text" x="350" y="640">Token？</text>
            
            <!-- 语法错误检查 -->
            <polygon class="decision" points="150,600 220,630 150,660 80,630"/>
            <text class="text small-text" x="150" y="625">语法</text>
            <text class="text small-text" x="150" y="640">正确？</text>
            
            <!-- 错误处理 -->
            <rect class="process" x="50" y="700" width="200" height="50" rx="5"/>
            <text class="text" x="150" y="720">输出错误信息</text>
            <text class="text small-text" x="150" y="735">和错误位置</text>
            
            <!-- 输出逆波兰式 -->
            <rect class="process" x="450" y="700" width="200" height="50" rx="5"/>
            <text class="text" x="550" y="720">输出完整的</text>
            <text class="text small-text" x="550" y="735">逆波兰式</text>
            
            <!-- 结束 -->
            <ellipse class="start-end" cx="350" cy="800" rx="60" ry="25"/>
            <text class="text" x="350" y="800">结束</text>
            
            <!-- 连接线和箭头 -->
            <!-- 开始到输入 -->
            <line class="flow-line" x1="350" y1="75" x2="350" y2="100"/>
            <polygon class="arrow" points="345,95 350,100 355,95"/>
            
            <!-- 输入到初始化 -->
            <line class="flow-line" x1="350" y1="150" x2="350" y2="180"/>
            <polygon class="arrow" points="345,175 350,180 355,175"/>
            
            <!-- 初始化到词法分析 -->
            <line class="flow-line" x1="350" y1="230" x2="350" y2="260"/>
            <polygon class="arrow" points="345,255 350,260 355,255"/>
            
            <!-- 词法分析到语法分析 -->
            <line class="flow-line" x1="350" y1="310" x2="350" y2="340"/>
            <polygon class="arrow" points="345,335 350,340 355,335"/>
            
            <!-- 语法分析到判断 -->
            <line class="flow-line" x1="350" y1="390" x2="350" y2="420"/>
            <polygon class="arrow" points="345,415 350,420 355,415"/>
            
            <!-- 判断到操作数 -->
            <line class="flow-line" x1="420" y1="450" x2="480" y2="450"/>
            <polygon class="arrow" points="475,445 480,450 475,455"/>
            <text class="label" x="450" y="440">操作数</text>
            
            <!-- 判断到运算符 -->
            <line class="flow-line" x1="280" y1="450" x2="250" y2="450"/>
            <polygon class="arrow" points="255,445 250,450 255,455"/>
            <text class="label" x="265" y="440">运算符</text>
            
            <!-- 操作数到语义动作 -->
            <line class="flow-line" x1="555" y1="475" x2="555" y2="545"/>
            <line class="flow-line" x1="555" y1="545" x2="450" y2="545"/>
            <polygon class="arrow" points="455,540 450,545 455,550"/>
            
            <!-- 运算符到语义动作 -->
            <line class="flow-line" x1="175" y1="475" x2="175" y2="545"/>
            <line class="flow-line" x1="175" y1="545" x2="250" y2="545"/>
            <polygon class="arrow" points="245,540 250,545 245,550"/>
            
            <!-- 语义动作到判断Token -->
            <line class="flow-line" x1="350" y1="570" x2="350" y2="600"/>
            <polygon class="arrow" points="345,595 350,600 355,595"/>
            
            <!-- 还有Token循环回去 -->
            <line class="flow-line" x1="420" y1="630" x2="500" y2="630"/>
            <line class="flow-line" x1="500" y1="630" x2="500" y2="380"/>
            <line class="flow-line" x1="500" y1="380" x2="420" y2="380"/>
            <line class="flow-line" x1="420" y1="380" x2="420" y2="420"/>
            <polygon class="arrow" points="415,415 420,420 425,415"/>
            <text class="label" x="460" y="620">是</text>
            
            <!-- 没有Token到语法检查 -->
            <line class="flow-line" x1="280" y1="630" x2="220" y2="630"/>
            <polygon class="arrow" points="225,625 220,630 225,635"/>
            <text class="label" x="250" y="620">否</text>
            
            <!-- 语法错误 -->
            <line class="flow-line" x1="80" y1="630" x2="50" y2="630"/>
            <line class="flow-line" x1="50" y1="630" x2="50" y2="725"/>
            <line class="flow-line" x1="50" y1="725" x2="50" y2="725"/>
            <polygon class="arrow" points="45,720 50,725 55,720"/>
            <text class="label" x="65" y="620">否</text>
            
            <!-- 语法正确 -->
            <line class="flow-line" x1="220" y1="630" x2="450" y2="630"/>
            <line class="flow-line" x1="450" y1="630" x2="450" y2="700"/>
            <polygon class="arrow" points="445,695 450,700 455,695"/>
            <text class="label" x="335" y="620">是</text>
            
            <!-- 错误处理到结束 -->
            <line class="flow-line" x1="150" y1="750" x2="150" y2="800"/>
            <line class="flow-line" x1="150" y1="800" x2="290" y2="800"/>
            <polygon class="arrow" points="285,795 290,800 285,805"/>
            
            <!-- 输出到结束 -->
            <line class="flow-line" x1="550" y1="750" x2="550" y2="800"/>
            <line class="flow-line" x1="550" y1="800" x2="410" y2="800"/>
            <polygon class="arrow" points="415,795 410,800 415,805"/>
        </svg>
    </div>
</body>
</html>
